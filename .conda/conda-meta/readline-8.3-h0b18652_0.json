{"build": "h0b18652_0", "build_number": 0, "channel": "https://repo.anaconda.com/pkgs/main/osx-arm64", "constrains": [], "depends": ["__osx >=11.1", "ncurses >=6.5,<7.0a0"], "extracted_package_dir": "/opt/homebrew/anaconda3/pkgs/readline-8.3-h0b18652_0", "files": ["include/readline/chardefs.h", "include/readline/history.h", "include/readline/keymaps.h", "include/readline/readline.h", "include/readline/rlconf.h", "include/readline/rlstdc.h", "include/readline/rltypedefs.h", "include/readline/tilde.h", "lib/libhistory.8.3.dylib", "lib/libhistory.8.dylib", "lib/libhistory.a", "lib/libhistory.dylib", "lib/libreadline.8.3.dylib", "lib/libreadline.8.dylib", "lib/libreadline.a", "lib/libreadline.dylib", "lib/pkgconfig/history.pc", "lib/pkgconfig/readline.pc", "share/doc/readline/CHANGES", "share/doc/readline/INSTALL", "share/doc/readline/README", "share/doc/readline/history.html", "share/doc/readline/readline.html", "share/doc/readline/rluserman.html", "share/info/history.info", "share/info/readline.info", "share/info/rluserman.info", "share/man/man3/history.3", "share/man/man3/readline.3"], "fn": "readline-8.3-h0b18652_0.conda", "license": "GPL-3.0-only", "link": {"source": "/opt/homebrew/anaconda3/pkgs/readline-8.3-h0b18652_0", "type": 1}, "md5": "fed853901ec41684b9e08b0c7ee4d7f0", "name": "readline", "package_tarball_full_path": "/opt/homebrew/anaconda3/pkgs/readline-8.3-h0b18652_0.conda", "paths_data": {"paths": [{"_path": "include/readline/chardefs.h", "path_type": "hardlink", "sha256": "9ba24bdf97311a56b49494aa17072965cae628390d648e207622f4068da960ff", "sha256_in_prefix": "9ba24bdf97311a56b49494aa17072965cae628390d648e207622f4068da960ff", "size_in_bytes": 4724}, {"_path": "include/readline/history.h", "path_type": "hardlink", "sha256": "5b8762c1086f62ed54567e258a72183b384534f57453d07f57085a03187a270a", "sha256_in_prefix": "5b8762c1086f62ed54567e258a72183b384534f57453d07f57085a03187a270a", "size_in_bytes": 10678}, {"_path": "include/readline/keymaps.h", "path_type": "hardlink", "sha256": "4c42c3c5d2bb3aba10c7e7f27dd79841f30857fde61c6c85bf67f4f2ac1befc4", "sha256_in_prefix": "4c42c3c5d2bb3aba10c7e7f27dd79841f30857fde61c6c85bf67f4f2ac1befc4", "size_in_bytes": 3201}, {"_path": "include/readline/readline.h", "path_type": "hardlink", "sha256": "1934a225c54300308cbda341851b693ecdbba66623e5f84527d90be9de34f572", "sha256_in_prefix": "1934a225c54300308cbda341851b693ecdbba66623e5f84527d90be9de34f572", "size_in_bytes": 39616}, {"_path": "include/readline/rlconf.h", "path_type": "hardlink", "sha256": "f140434f57cba8dbd83e87997c73608b51643b6dd89f292aa5eb83a68c816dd2", "sha256_in_prefix": "f140434f57cba8dbd83e87997c73608b51643b6dd89f292aa5eb83a68c816dd2", "size_in_bytes": 3044}, {"_path": "include/readline/rlstdc.h", "path_type": "hardlink", "sha256": "5a66bb179f4a7ad32b9a0a04b0a60c20c0b3f5ee63b1b2eed41fc64fbd590e64", "sha256_in_prefix": "5a66bb179f4a7ad32b9a0a04b0a60c20c0b3f5ee63b1b2eed41fc64fbd590e64", "size_in_bytes": 1551}, {"_path": "include/readline/rltypedefs.h", "path_type": "hardlink", "sha256": "9c756ee45536c3999c3d9b50c75343fe0338d7634ca96c3b375d13e30e9c582e", "sha256_in_prefix": "9c756ee45536c3999c3d9b50c75343fe0338d7634ca96c3b375d13e30e9c582e", "size_in_bytes": 3064}, {"_path": "include/readline/tilde.h", "path_type": "hardlink", "sha256": "76ea2566b9045468f1ae1a0a97e62d765edcd642a2d1f12a62a6af1b4dfe9729", "sha256_in_prefix": "76ea2566b9045468f1ae1a0a97e62d765edcd642a2d1f12a62a6af1b4dfe9729", "size_in_bytes": 2652}, {"_path": "lib/libhistory.8.3.dylib", "path_type": "hardlink", "sha256": "5ff26e88043e14da02be9ab240e034379e079a2e9ced7c7ccaf29acbbb0b9d93", "sha256_in_prefix": "5ff26e88043e14da02be9ab240e034379e079a2e9ced7c7ccaf29acbbb0b9d93", "size_in_bytes": 92096}, {"_path": "lib/libhistory.8.dylib", "path_type": "softlink", "sha256": "5ff26e88043e14da02be9ab240e034379e079a2e9ced7c7ccaf29acbbb0b9d93", "size_in_bytes": 92096}, {"_path": "lib/libhistory.a", "path_type": "hardlink", "sha256": "f2102cb72601adec02cdc9b9f813219390c14943d3bf9dbaaf7084f68c630997", "sha256_in_prefix": "f2102cb72601adec02cdc9b9f813219390c14943d3bf9dbaaf7084f68c630997", "size_in_bytes": 53456}, {"_path": "lib/libhistory.dylib", "path_type": "softlink", "sha256": "5ff26e88043e14da02be9ab240e034379e079a2e9ced7c7ccaf29acbbb0b9d93", "size_in_bytes": 92096}, {"_path": "lib/libreadline.8.3.dylib", "path_type": "hardlink", "sha256": "7f4ff85bc386c0acd8b1ca2d81688725f8e712d05611c9a8f4ddbdd3a18c4bb2", "sha256_in_prefix": "7f4ff85bc386c0acd8b1ca2d81688725f8e712d05611c9a8f4ddbdd3a18c4bb2", "size_in_bytes": 348576}, {"_path": "lib/libreadline.8.dylib", "path_type": "softlink", "sha256": "7f4ff85bc386c0acd8b1ca2d81688725f8e712d05611c9a8f4ddbdd3a18c4bb2", "size_in_bytes": 348576}, {"_path": "lib/libreadline.a", "path_type": "hardlink", "sha256": "a8c33ae94825267e11122846d21a4ebce4b41bc890a518cb5d6379be026d26a0", "sha256_in_prefix": "a8c33ae94825267e11122846d21a4ebce4b41bc890a518cb5d6379be026d26a0", "size_in_bytes": 542288}, {"_path": "lib/libreadline.dylib", "path_type": "softlink", "sha256": "7f4ff85bc386c0acd8b1ca2d81688725f8e712d05611c9a8f4ddbdd3a18c4bb2", "size_in_bytes": 348576}, {"_path": "lib/pkgconfig/history.pc", "file_mode": "text", "path_type": "hardlink", "prefix_placeholder": "/var/folders/nz/j6p8yfhx1mv_0grj5xl4650h0000gp/T/abs_14q9fvz_55/croot/readline_1754485616628/_h_env_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_place", "sha256": "4e3495dd22c8272d2a26721c6f7dde68fdf32e4a73dd752a978939937c0fd278", "sha256_in_prefix": "9767e4c26b19056e1de6abce3a2aa3f7c2fc1675f5515c094062b52f9eec41b4", "size_in_bytes": 548}, {"_path": "lib/pkgconfig/readline.pc", "file_mode": "text", "path_type": "hardlink", "prefix_placeholder": "/var/folders/nz/j6p8yfhx1mv_0grj5xl4650h0000gp/T/abs_14q9fvz_55/croot/readline_1754485616628/_h_env_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_place", "sha256": "6283b4ad7cae3a389727ef459f2817076ad6cc26f3a29bf39a9a8fd0b6247cde", "sha256_in_prefix": "1fd4047aceb1fe91995df1c9314298ba7ba0301055be0caec3679f7b436943a7", "size_in_bytes": 564}, {"_path": "share/doc/readline/CHANGES", "path_type": "hardlink", "sha256": "f74dff0f05bb20311c53287b02866f343e69cd8bf9d4e58230e3f8d31e2dd7d9", "sha256_in_prefix": "f74dff0f05bb20311c53287b02866f343e69cd8bf9d4e58230e3f8d31e2dd7d9", "size_in_bytes": 88142}, {"_path": "share/doc/readline/INSTALL", "path_type": "hardlink", "sha256": "c0b93d8533fbf743e24f93b7f6bc26ba1a4e5e292117289a891e728454af2fb5", "sha256_in_prefix": "c0b93d8533fbf743e24f93b7f6bc26ba1a4e5e292117289a891e728454af2fb5", "size_in_bytes": 14330}, {"_path": "share/doc/readline/README", "path_type": "hardlink", "sha256": "c66390595e51c1ab99aeac37ee9cd5f7cd652e1ee5da86dea7682f769205e57c", "sha256_in_prefix": "c66390595e51c1ab99aeac37ee9cd5f7cd652e1ee5da86dea7682f769205e57c", "size_in_bytes": 8146}, {"_path": "share/doc/readline/history.html", "path_type": "hardlink", "sha256": "2eec7a7084f73aec15e6e1ad103e6c3ca36ab384bb6cb55123a3c08e858aa08c", "sha256_in_prefix": "2eec7a7084f73aec15e6e1ad103e6c3ca36ab384bb6cb55123a3c08e858aa08c", "size_in_bytes": 116091}, {"_path": "share/doc/readline/readline.html", "path_type": "hardlink", "sha256": "44f209944f7f1166589f2d417bb0904034ecac69db05eeeffc5f4b79a908af31", "sha256_in_prefix": "44f209944f7f1166589f2d417bb0904034ecac69db05eeeffc5f4b79a908af31", "size_in_bytes": 481447}, {"_path": "share/doc/readline/rluserman.html", "path_type": "hardlink", "sha256": "11bace2423451564da95cb4e0d2ef1be5f1759f6d6d8e7737bfdaceb6f11c26b", "sha256_in_prefix": "11bace2423451564da95cb4e0d2ef1be5f1759f6d6d8e7737bfdaceb6f11c26b", "size_in_bytes": 151722}, {"_path": "share/info/history.info", "path_type": "hardlink", "sha256": "f547b3c8547ee4092941f088ccbb1301eb40a509bca39708f8dca87627563b69", "sha256_in_prefix": "f547b3c8547ee4092941f088ccbb1301eb40a509bca39708f8dca87627563b69", "size_in_bytes": 66323}, {"_path": "share/info/readline.info", "path_type": "hardlink", "sha256": "500c11a34c367045aa1a37f3f1add3efe00d72386cf1ae911a781e5472a3a0fb", "sha256_in_prefix": "500c11a34c367045aa1a37f3f1add3efe00d72386cf1ae911a781e5472a3a0fb", "size_in_bytes": 258713}, {"_path": "share/info/rluserman.info", "path_type": "hardlink", "sha256": "aff0a840a4a1e52912abf0890e063259f69a4550cbfc800b605f4a5bc4bf637b", "sha256_in_prefix": "aff0a840a4a1e52912abf0890e063259f69a4550cbfc800b605f4a5bc4bf637b", "size_in_bytes": 100612}, {"_path": "share/man/man3/history.3", "path_type": "hardlink", "sha256": "49625c9f34dbedc1b4881fe36f10459849cc9b39a479d067e191a470b0ce8a94", "sha256_in_prefix": "49625c9f34dbedc1b4881fe36f10459849cc9b39a479d067e191a470b0ce8a94", "size_in_bytes": 26594}, {"_path": "share/man/man3/readline.3", "path_type": "hardlink", "sha256": "2254c883f9d0af108d93a20ddae98363f82885d2ab36f08b34d76ae6eaec22bc", "sha256_in_prefix": "2254c883f9d0af108d93a20ddae98363f82885d2ab36f08b34d76ae6eaec22bc", "size_in_bytes": 60431}], "paths_version": 1}, "requested_spec": "None", "sha256": "22cf28d17df12004caeeaae6ec93a01e3f855eafece0b32a98976eef08968a34", "size": 475276, "subdir": "osx-arm64", "timestamp": 1754485689000, "url": "https://repo.anaconda.com/pkgs/main/osx-arm64/readline-8.3-h0b18652_0.conda", "version": "8.3"}