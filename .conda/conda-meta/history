==> 2025-08-07 15:46:56 <==
# cmd: /opt/homebrew/anaconda3/lib/python3.12/site-packages/conda/__main__.py create --yes --prefix .conda python=3.11
# conda version: 24.9.2
+defaults/noarch::pip-25.1-pyhc872135_2
+defaults/noarch::tzdata-2025b-h04d1e81_0
+defaults/osx-arm64::bzip2-1.0.8-h80987f9_6
+defaults/osx-arm64::ca-certificates-2025.2.25-hca03da5_0
+defaults/osx-arm64::expat-2.7.1-h313beb8_0
+defaults/osx-arm64::libcxx-17.0.6-he5c5206_4
+defaults/osx-arm64::libffi-3.4.4-hca03da5_1
+defaults/osx-arm64::ncurses-6.5-hee39554_0
+defaults/osx-arm64::openssl-3.0.17-h4ee41c1_0
+defaults/osx-arm64::python-3.11.13-h19e8193_0
+defaults/osx-arm64::readline-8.3-h0b18652_0
+defaults/osx-arm64::setuptools-78.1.1-py311hca03da5_0
+defaults/osx-arm64::sqlite-3.50.2-h79febb2_1
+defaults/osx-arm64::tk-8.6.14-h6ba3021_1
+defaults/osx-arm64::wheel-0.45.1-py311hca03da5_0
+defaults/osx-arm64::xz-5.6.4-h80987f9_1
+defaults/osx-arm64::zlib-1.2.13-h18a0788_1
# update specs: ['python=3.11']
