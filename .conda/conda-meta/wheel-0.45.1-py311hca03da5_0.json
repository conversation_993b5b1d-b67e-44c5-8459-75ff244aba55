{"build": "py311hca03da5_0", "build_number": 0, "channel": "https://repo.anaconda.com/pkgs/main/osx-arm64", "constrains": [], "depends": ["python >=3.11,<3.12.0a0"], "extracted_package_dir": "/opt/homebrew/anaconda3/pkgs/wheel-0.45.1-py311hca03da5_0", "files": ["bin/wheel", "lib/python3.11/site-packages/wheel-0.45.1.dist-info/LICENSE.txt", "lib/python3.11/site-packages/wheel-0.45.1.dist-info/METADATA", "lib/python3.11/site-packages/wheel-0.45.1.dist-info/RECORD", "lib/python3.11/site-packages/wheel-0.45.1.dist-info/WHEEL", "lib/python3.11/site-packages/wheel-0.45.1.dist-info/entry_points.txt", "lib/python3.11/site-packages/wheel/__init__.py", "lib/python3.11/site-packages/wheel/__main__.py", "lib/python3.11/site-packages/wheel/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/wheel/__pycache__/__main__.cpython-311.pyc", "lib/python3.11/site-packages/wheel/__pycache__/_bdist_wheel.cpython-311.pyc", "lib/python3.11/site-packages/wheel/__pycache__/_setuptools_logging.cpython-311.pyc", "lib/python3.11/site-packages/wheel/__pycache__/bdist_wheel.cpython-311.pyc", "lib/python3.11/site-packages/wheel/__pycache__/macosx_libfile.cpython-311.pyc", "lib/python3.11/site-packages/wheel/__pycache__/metadata.cpython-311.pyc", "lib/python3.11/site-packages/wheel/__pycache__/util.cpython-311.pyc", "lib/python3.11/site-packages/wheel/__pycache__/wheelfile.cpython-311.pyc", "lib/python3.11/site-packages/wheel/_bdist_wheel.py", "lib/python3.11/site-packages/wheel/_setuptools_logging.py", "lib/python3.11/site-packages/wheel/bdist_wheel.py", "lib/python3.11/site-packages/wheel/cli/__init__.py", "lib/python3.11/site-packages/wheel/cli/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/wheel/cli/__pycache__/convert.cpython-311.pyc", "lib/python3.11/site-packages/wheel/cli/__pycache__/pack.cpython-311.pyc", "lib/python3.11/site-packages/wheel/cli/__pycache__/tags.cpython-311.pyc", "lib/python3.11/site-packages/wheel/cli/__pycache__/unpack.cpython-311.pyc", "lib/python3.11/site-packages/wheel/cli/convert.py", "lib/python3.11/site-packages/wheel/cli/pack.py", "lib/python3.11/site-packages/wheel/cli/tags.py", "lib/python3.11/site-packages/wheel/cli/unpack.py", "lib/python3.11/site-packages/wheel/macosx_libfile.py", "lib/python3.11/site-packages/wheel/metadata.py", "lib/python3.11/site-packages/wheel/util.py", "lib/python3.11/site-packages/wheel/vendored/__init__.py", "lib/python3.11/site-packages/wheel/vendored/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/wheel/vendored/packaging/LICENSE", "lib/python3.11/site-packages/wheel/vendored/packaging/LICENSE.APACHE", "lib/python3.11/site-packages/wheel/vendored/packaging/LICENSE.BSD", "lib/python3.11/site-packages/wheel/vendored/packaging/__init__.py", "lib/python3.11/site-packages/wheel/vendored/packaging/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/wheel/vendored/packaging/__pycache__/_elffile.cpython-311.pyc", "lib/python3.11/site-packages/wheel/vendored/packaging/__pycache__/_manylinux.cpython-311.pyc", "lib/python3.11/site-packages/wheel/vendored/packaging/__pycache__/_musllinux.cpython-311.pyc", "lib/python3.11/site-packages/wheel/vendored/packaging/__pycache__/_parser.cpython-311.pyc", "lib/python3.11/site-packages/wheel/vendored/packaging/__pycache__/_structures.cpython-311.pyc", "lib/python3.11/site-packages/wheel/vendored/packaging/__pycache__/_tokenizer.cpython-311.pyc", "lib/python3.11/site-packages/wheel/vendored/packaging/__pycache__/markers.cpython-311.pyc", "lib/python3.11/site-packages/wheel/vendored/packaging/__pycache__/requirements.cpython-311.pyc", "lib/python3.11/site-packages/wheel/vendored/packaging/__pycache__/specifiers.cpython-311.pyc", "lib/python3.11/site-packages/wheel/vendored/packaging/__pycache__/tags.cpython-311.pyc", "lib/python3.11/site-packages/wheel/vendored/packaging/__pycache__/utils.cpython-311.pyc", "lib/python3.11/site-packages/wheel/vendored/packaging/__pycache__/version.cpython-311.pyc", "lib/python3.11/site-packages/wheel/vendored/packaging/_elffile.py", "lib/python3.11/site-packages/wheel/vendored/packaging/_manylinux.py", "lib/python3.11/site-packages/wheel/vendored/packaging/_musllinux.py", "lib/python3.11/site-packages/wheel/vendored/packaging/_parser.py", "lib/python3.11/site-packages/wheel/vendored/packaging/_structures.py", "lib/python3.11/site-packages/wheel/vendored/packaging/_tokenizer.py", "lib/python3.11/site-packages/wheel/vendored/packaging/markers.py", "lib/python3.11/site-packages/wheel/vendored/packaging/requirements.py", "lib/python3.11/site-packages/wheel/vendored/packaging/specifiers.py", "lib/python3.11/site-packages/wheel/vendored/packaging/tags.py", "lib/python3.11/site-packages/wheel/vendored/packaging/utils.py", "lib/python3.11/site-packages/wheel/vendored/packaging/version.py", "lib/python3.11/site-packages/wheel/vendored/vendor.txt", "lib/python3.11/site-packages/wheel/wheelfile.py"], "fn": "wheel-0.45.1-py311hca03da5_0.conda", "license": "MIT", "link": {"source": "/opt/homebrew/anaconda3/pkgs/wheel-0.45.1-py311hca03da5_0", "type": 1}, "md5": "4470de7e3f40c084457285b0c6c4f2de", "name": "wheel", "package_tarball_full_path": "/opt/homebrew/anaconda3/pkgs/wheel-0.45.1-py311hca03da5_0.conda", "paths_data": {"paths": [{"_path": "bin/wheel", "file_mode": "text", "path_type": "hardlink", "prefix_placeholder": "/var/folders/nz/j6p8yfhx1mv_0grj5xl4650h0000gp/T/abs_7aou3fg_ke/croot/wheel_1737990417862/_h_env_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehol", "sha256": "4b6c258712ae16d2d5b04d9fec7008f66b66a7ccda153e9137a01a1bb4d2733d", "sha256_in_prefix": "432332e5aed979256dda494c09c95500375ce00ba62daddd2bfec141b262d50e", "size_in_bytes": 462}, {"_path": "lib/python3.11/site-packages/wheel-0.45.1.dist-info/LICENSE.txt", "path_type": "hardlink", "sha256": "30c23618679108f3e8ea1d2a658c7ca417bdfc891c98ef1a89fa4ff0c9828654", "sha256_in_prefix": "30c23618679108f3e8ea1d2a658c7ca417bdfc891c98ef1a89fa4ff0c9828654", "size_in_bytes": 1107}, {"_path": "lib/python3.11/site-packages/wheel-0.45.1.dist-info/METADATA", "path_type": "hardlink", "sha256": "b04e96136b82c11487b79640c093a7344af76607ec497a24f4b87b2518590a60", "sha256_in_prefix": "b04e96136b82c11487b79640c093a7344af76607ec497a24f4b87b2518590a60", "size_in_bytes": 2313}, {"_path": "lib/python3.11/site-packages/wheel-0.45.1.dist-info/RECORD", "path_type": "hardlink", "sha256": "7c8a58bf3d056258750bf926332679ec2407473a6b3f80cbb6184582f543e841", "sha256_in_prefix": "7c8a58bf3d056258750bf926332679ec2407473a6b3f80cbb6184582f543e841", "size_in_bytes": 3180}, {"_path": "lib/python3.11/site-packages/wheel-0.45.1.dist-info/WHEEL", "path_type": "hardlink", "sha256": "1196c6921ec87b83e865f450f08d19b8ff5592537f4ef719e83484e546abe33e", "sha256_in_prefix": "1196c6921ec87b83e865f450f08d19b8ff5592537f4ef719e83484e546abe33e", "size_in_bytes": 81}, {"_path": "lib/python3.11/site-packages/wheel-0.45.1.dist-info/entry_points.txt", "path_type": "hardlink", "sha256": "ad363505b90f1e1906326e10dc5d29233241cd6da4331a06d68ae27dfbc6740d", "sha256_in_prefix": "ad363505b90f1e1906326e10dc5d29233241cd6da4331a06d68ae27dfbc6740d", "size_in_bytes": 104}, {"_path": "lib/python3.11/site-packages/wheel/__init__.py", "path_type": "hardlink", "sha256": "9abc4c9ef757002babfcb59e81b51f879839cac599addeb75099fcf74c2f18d9", "sha256_in_prefix": "9abc4c9ef757002babfcb59e81b51f879839cac599addeb75099fcf74c2f18d9", "size_in_bytes": 59}, {"_path": "lib/python3.11/site-packages/wheel/__main__.py", "path_type": "hardlink", "sha256": "3643149ee4c219c3a4818d0804b8010950bf04619c58e471d8af236064b5d941", "sha256_in_prefix": "3643149ee4c219c3a4818d0804b8010950bf04619c58e471d8af236064b5d941", "size_in_bytes": 455}, {"_path": "lib/python3.11/site-packages/wheel/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "7b28ca1a36b8f944659c71712db87c44a58bc874cb15e924ed60b7f7d8cc2bc5", "sha256_in_prefix": "7b28ca1a36b8f944659c71712db87c44a58bc874cb15e924ed60b7f7d8cc2bc5", "size_in_bytes": 243}, {"_path": "lib/python3.11/site-packages/wheel/__pycache__/__main__.cpython-311.pyc", "path_type": "hardlink", "sha256": "f19b163bb2303cbba56dc428a8259b5bde6fd895aae5100e904bfe300a79394b", "sha256_in_prefix": "f19b163bb2303cbba56dc428a8259b5bde6fd895aae5100e904bfe300a79394b", "size_in_bytes": 1048}, {"_path": "lib/python3.11/site-packages/wheel/__pycache__/_bdist_wheel.cpython-311.pyc", "path_type": "hardlink", "sha256": "0243ee44057d917e9517975bc28d1b738ec2b434913e369fb340533e437d937f", "sha256_in_prefix": "0243ee44057d917e9517975bc28d1b738ec2b434913e369fb340533e437d937f", "size_in_bytes": 28529}, {"_path": "lib/python3.11/site-packages/wheel/__pycache__/_setuptools_logging.cpython-311.pyc", "path_type": "hardlink", "sha256": "164edb6d9f5da595f70f0c53a154d383477dc6b62eae207b5fd660fadf34f916", "sha256_in_prefix": "164edb6d9f5da595f70f0c53a154d383477dc6b62eae207b5fd660fadf34f916", "size_in_bytes": 1460}, {"_path": "lib/python3.11/site-packages/wheel/__pycache__/bdist_wheel.cpython-311.pyc", "path_type": "hardlink", "sha256": "ea977c0996c91ee8de72114ab9e1e020aee845f97505263a02913b3f9968a3e1", "sha256_in_prefix": "ea977c0996c91ee8de72114ab9e1e020aee845f97505263a02913b3f9968a3e1", "size_in_bytes": 876}, {"_path": "lib/python3.11/site-packages/wheel/__pycache__/macosx_libfile.cpython-311.pyc", "path_type": "hardlink", "sha256": "5497d623c68b0d84f8d24f573dbd62c77fdc0907b6f65284ed7751127b6bb6b1", "sha256_in_prefix": "5497d623c68b0d84f8d24f573dbd62c77fdc0907b6f65284ed7751127b6bb6b1", "size_in_bytes": 17803}, {"_path": "lib/python3.11/site-packages/wheel/__pycache__/metadata.cpython-311.pyc", "path_type": "hardlink", "sha256": "d0183acb3084009def53abb0b20c30d15a6381bd6eda1bdde9d947d179f2a4f8", "sha256_in_prefix": "d0183acb3084009def53abb0b20c30d15a6381bd6eda1bdde9d947d179f2a4f8", "size_in_bytes": 9698}, {"_path": "lib/python3.11/site-packages/wheel/__pycache__/util.cpython-311.pyc", "path_type": "hardlink", "sha256": "5adc1234a3262c850e80d9c1afd5b73d1ca22db02a9423aeed29b252895e367b", "sha256_in_prefix": "5adc1234a3262c850e80d9c1afd5b73d1ca22db02a9423aeed29b252895e367b", "size_in_bytes": 1007}, {"_path": "lib/python3.11/site-packages/wheel/__pycache__/wheelfile.cpython-311.pyc", "path_type": "hardlink", "sha256": "d494fccebae9f85019387fe095f0404b1166619003ac37fc3a47a2bb786dcc80", "sha256_in_prefix": "d494fccebae9f85019387fe095f0404b1166619003ac37fc3a47a2bb786dcc80", "size_in_bytes": 12542}, {"_path": "lib/python3.11/site-packages/wheel/_bdist_wheel.py", "path_type": "hardlink", "sha256": "520842423487fe955f71987aa118f34b0fd342171fdda9d2c753a488b48bf363", "sha256_in_prefix": "520842423487fe955f71987aa118f34b0fd342171fdda9d2c753a488b48bf363", "size_in_bytes": 21694}, {"_path": "lib/python3.11/site-packages/wheel/_setuptools_logging.py", "path_type": "hardlink", "sha256": "fb9282fa59ded2294e5162037ce92a6a951618c15986e2980c86af219881e643", "sha256_in_prefix": "fb9282fa59ded2294e5162037ce92a6a951618c15986e2980c86af219881e643", "size_in_bytes": 781}, {"_path": "lib/python3.11/site-packages/wheel/bdist_wheel.py", "path_type": "hardlink", "sha256": "b697fd5ae7e248ed51b84320e683e121f486f0333388267fe26b82285ebd0aaa", "sha256_in_prefix": "b697fd5ae7e248ed51b84320e683e121f486f0333388267fe26b82285ebd0aaa", "size_in_bytes": 1107}, {"_path": "lib/python3.11/site-packages/wheel/cli/__init__.py", "path_type": "hardlink", "sha256": "369abafe32a2d3776121c46799bb85870be2549c703b4b5812712158cbfd709a", "sha256_in_prefix": "369abafe32a2d3776121c46799bb85870be2549c703b4b5812712158cbfd709a", "size_in_bytes": 4402}, {"_path": "lib/python3.11/site-packages/wheel/cli/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "05040d94495e6e7065a640bc88680f3fc2ea32360768b11776d6a14364c11fd5", "sha256_in_prefix": "05040d94495e6e7065a640bc88680f3fc2ea32360768b11776d6a14364c11fd5", "size_in_bytes": 7766}, {"_path": "lib/python3.11/site-packages/wheel/cli/__pycache__/convert.cpython-311.pyc", "path_type": "hardlink", "sha256": "376f32360daaa86edf7116d5f50b05144b8f4ecfb4bd6443fe911997e18a6777", "sha256_in_prefix": "376f32360daaa86edf7116d5f50b05144b8f4ecfb4bd6443fe911997e18a6777", "size_in_bytes": 18549}, {"_path": "lib/python3.11/site-packages/wheel/cli/__pycache__/pack.cpython-311.pyc", "path_type": "hardlink", "sha256": "ea634941e58dd81d64365c22c98e333a4e2b43e7d056097204899984cd8c0191", "sha256_in_prefix": "ea634941e58dd81d64365c22c98e333a4e2b43e7d056097204899984cd8c0191", "size_in_bytes": 5824}, {"_path": "lib/python3.11/site-packages/wheel/cli/__pycache__/tags.cpython-311.pyc", "path_type": "hardlink", "sha256": "86b4ea95b6b1cacdc4338712713b33bbde51231521813289f54583c1eb12fe41", "sha256_in_prefix": "86b4ea95b6b1cacdc4338712713b33bbde51231521813289f54583c1eb12fe41", "size_in_bytes": 7920}, {"_path": "lib/python3.11/site-packages/wheel/cli/__pycache__/unpack.cpython-311.pyc", "path_type": "hardlink", "sha256": "b77fc4b74d095963135c1fc1ffdc13e4299d278d47bebd12117ef943f32edec0", "sha256_in_prefix": "b77fc4b74d095963135c1fc1ffdc13e4299d278d47bebd12117ef943f32edec0", "size_in_bytes": 1748}, {"_path": "lib/python3.11/site-packages/wheel/cli/convert.py", "path_type": "hardlink", "sha256": "062d27b445dbf674e5942c56793450e23fa73ecdeccd64842a2a46fc68273244", "sha256_in_prefix": "062d27b445dbf674e5942c56793450e23fa73ecdeccd64842a2a46fc68273244", "size_in_bytes": 12634}, {"_path": "lib/python3.11/site-packages/wheel/cli/pack.py", "path_type": "hardlink", "sha256": "08015c1dd055ba5bec1d82659dd2953bb28c23d26a053673e628b43cac7108eb", "sha256_in_prefix": "08015c1dd055ba5bec1d82659dd2953bb28c23d26a053673e628b43cac7108eb", "size_in_bytes": 3103}, {"_path": "lib/python3.11/site-packages/wheel/cli/tags.py", "path_type": "hardlink", "sha256": "947c3e2da5ab912e49cbfa96730fbaa528de34ceb20230e7a8a2371392534c25", "sha256_in_prefix": "947c3e2da5ab912e49cbfa96730fbaa528de34ceb20230e7a8a2371392534c25", "size_in_bytes": 4760}, {"_path": "lib/python3.11/site-packages/wheel/cli/unpack.py", "path_type": "hardlink", "sha256": "63f27bca7c4f4a81454d3ec7d1f3206c195512bc320c670e6e099ee4c06ecf9f", "sha256_in_prefix": "63f27bca7c4f4a81454d3ec7d1f3206c195512bc320c670e6e099ee4c06ecf9f", "size_in_bytes": 1021}, {"_path": "lib/python3.11/site-packages/wheel/macosx_libfile.py", "path_type": "hardlink", "sha256": "935c7b084dcb3ed3951aa8fa3574359d319854f69e46b855cd41bf28fab7cc3b", "sha256_in_prefix": "935c7b084dcb3ed3951aa8fa3574359d319854f69e46b855cd41bf28fab7cc3b", "size_in_bytes": 16572}, {"_path": "lib/python3.11/site-packages/wheel/metadata.py", "path_type": "hardlink", "sha256": "242e29ee395066ed9b513010d9f7af92a2e383f5fa8273724612e7e8e50ed6d7", "sha256_in_prefix": "242e29ee395066ed9b513010d9f7af92a2e383f5fa8273724612e7e8e50ed6d7", "size_in_bytes": 6171}, {"_path": "lib/python3.11/site-packages/wheel/util.py", "path_type": "hardlink", "sha256": "68beda89b1f061481f73c5a5a252f9b577645780dab5b2716476f59301c52405", "sha256_in_prefix": "68beda89b1f061481f73c5a5a252f9b577645780dab5b2716476f59301c52405", "size_in_bytes": 423}, {"_path": "lib/python3.11/site-packages/wheel/vendored/__init__.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "lib/python3.11/site-packages/wheel/vendored/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "78fead6a68a1fed7b4effbda9a26a72525f09e33233cae476bddcaa8a72ea472", "sha256_in_prefix": "78fead6a68a1fed7b4effbda9a26a72525f09e33233cae476bddcaa8a72ea472", "size_in_bytes": 164}, {"_path": "lib/python3.11/site-packages/wheel/vendored/packaging/LICENSE", "path_type": "hardlink", "sha256": "cad1ef5bd340d73e074ba614d26f7deaca5c7940c3d8c34852e65c4909686c48", "sha256_in_prefix": "cad1ef5bd340d73e074ba614d26f7deaca5c7940c3d8c34852e65c4909686c48", "size_in_bytes": 197}, {"_path": "lib/python3.11/site-packages/wheel/vendored/packaging/LICENSE.APACHE", "path_type": "hardlink", "sha256": "0d542e0c8804e39aa7f37eb00da5a762149dc682d7829451287e11b938e94594", "sha256_in_prefix": "0d542e0c8804e39aa7f37eb00da5a762149dc682d7829451287e11b938e94594", "size_in_bytes": 10174}, {"_path": "lib/python3.11/site-packages/wheel/vendored/packaging/LICENSE.BSD", "path_type": "hardlink", "sha256": "b70e7e9b742f1cc6f948b34c16aa39ffece94196364bc88ff0d2180f0028fac5", "sha256_in_prefix": "b70e7e9b742f1cc6f948b34c16aa39ffece94196364bc88ff0d2180f0028fac5", "size_in_bytes": 1344}, {"_path": "lib/python3.11/site-packages/wheel/vendored/packaging/__init__.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "lib/python3.11/site-packages/wheel/vendored/packaging/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "13e5779991ed2b55e20b078e2c36b652ef4bd46d516b754e402a4118c167ab6a", "sha256_in_prefix": "13e5779991ed2b55e20b078e2c36b652ef4bd46d516b754e402a4118c167ab6a", "size_in_bytes": 174}, {"_path": "lib/python3.11/site-packages/wheel/vendored/packaging/__pycache__/_elffile.cpython-311.pyc", "path_type": "hardlink", "sha256": "1d12159fa0c903a15b35ee56a8252f9a6217a691254571ae8fdb003dfaa2a402", "sha256_in_prefix": "1d12159fa0c903a15b35ee56a8252f9a6217a691254571ae8fdb003dfaa2a402", "size_in_bytes": 5454}, {"_path": "lib/python3.11/site-packages/wheel/vendored/packaging/__pycache__/_manylinux.cpython-311.pyc", "path_type": "hardlink", "sha256": "306a21c2e519dc911523cc36d7a8fed2866dd4f9a786688510ab601737394f76", "sha256_in_prefix": "306a21c2e519dc911523cc36d7a8fed2866dd4f9a786688510ab601737394f76", "size_in_bytes": 11050}, {"_path": "lib/python3.11/site-packages/wheel/vendored/packaging/__pycache__/_musllinux.cpython-311.pyc", "path_type": "hardlink", "sha256": "7c992503c710ca103faae2bf76b6bc7103cd084c3e28b2169629e3b3b19db640", "sha256_in_prefix": "7c992503c710ca103faae2bf76b6bc7103cd084c3e28b2169629e3b3b19db640", "size_in_bytes": 5265}, {"_path": "lib/python3.11/site-packages/wheel/vendored/packaging/__pycache__/_parser.cpython-311.pyc", "path_type": "hardlink", "sha256": "af0ffa02a4834f92c5b7499c9d6b0061b4da2a6ac377974cc4027fdc95ce10d9", "sha256_in_prefix": "af0ffa02a4834f92c5b7499c9d6b0061b4da2a6ac377974cc4027fdc95ce10d9", "size_in_bytes": 16288}, {"_path": "lib/python3.11/site-packages/wheel/vendored/packaging/__pycache__/_structures.cpython-311.pyc", "path_type": "hardlink", "sha256": "9a25261521dc6ca92efbbc56a45fc88dbf652f268e598cb3e86baf14eee60234", "sha256_in_prefix": "9a25261521dc6ca92efbbc56a45fc88dbf652f268e598cb3e86baf14eee60234", "size_in_bytes": 3658}, {"_path": "lib/python3.11/site-packages/wheel/vendored/packaging/__pycache__/_tokenizer.cpython-311.pyc", "path_type": "hardlink", "sha256": "c9e44c869fc816fa90bdb8e9b29dadf73271a024398f4864671afba32b12e1bb", "sha256_in_prefix": "c9e44c869fc816fa90bdb8e9b29dadf73271a024398f4864671afba32b12e1bb", "size_in_bytes": 8635}, {"_path": "lib/python3.11/site-packages/wheel/vendored/packaging/__pycache__/markers.cpython-311.pyc", "path_type": "hardlink", "sha256": "e2c1031ea6a66adef4a69c01990a59ff3bb5e2acdcf632aac9eba88fb4c26cd1", "sha256_in_prefix": "e2c1031ea6a66adef4a69c01990a59ff3bb5e2acdcf632aac9eba88fb4c26cd1", "size_in_bytes": 12023}, {"_path": "lib/python3.11/site-packages/wheel/vendored/packaging/__pycache__/requirements.cpython-311.pyc", "path_type": "hardlink", "sha256": "c6986b1de33f2b7663fd184bb8a3b167f268e96619b3e4fcf7078c3d77aab285", "sha256_in_prefix": "c6986b1de33f2b7663fd184bb8a3b167f268e96619b3e4fcf7078c3d77aab285", "size_in_bytes": 4693}, {"_path": "lib/python3.11/site-packages/wheel/vendored/packaging/__pycache__/specifiers.cpython-311.pyc", "path_type": "hardlink", "sha256": "b6cdb26af7f916bfcc3df5381d566756cd81d17712f382ac26926eb91563c86a", "sha256_in_prefix": "b6cdb26af7f916bfcc3df5381d566756cd81d17712f382ac26926eb91563c86a", "size_in_bytes": 42008}, {"_path": "lib/python3.11/site-packages/wheel/vendored/packaging/__pycache__/tags.cpython-311.pyc", "path_type": "hardlink", "sha256": "065e647b8c32d6b67616e381d315ecaf108134338431530458ac2fa61fca5788", "sha256_in_prefix": "065e647b8c32d6b67616e381d315ecaf108134338431530458ac2fa61fca5788", "size_in_bytes": 24604}, {"_path": "lib/python3.11/site-packages/wheel/vendored/packaging/__pycache__/utils.cpython-311.pyc", "path_type": "hardlink", "sha256": "b21404aab770b08a292fa0f95cdff79c2bc1a922b6b49d6dfdf681d9aa1d7034", "sha256_in_prefix": "b21404aab770b08a292fa0f95cdff79c2bc1a922b6b49d6dfdf681d9aa1d7034", "size_in_bytes": 8249}, {"_path": "lib/python3.11/site-packages/wheel/vendored/packaging/__pycache__/version.cpython-311.pyc", "path_type": "hardlink", "sha256": "ce752f3139b02ff10a6a0790ae0a04a28e318c9d2312494ddcac019f5f8528ea", "sha256_in_prefix": "ce752f3139b02ff10a6a0790ae0a04a28e318c9d2312494ddcac019f5f8528ea", "size_in_bytes": 21426}, {"_path": "lib/python3.11/site-packages/wheel/vendored/packaging/_elffile.py", "path_type": "hardlink", "sha256": "85b98af0e0fa67b7d8ea1c229c7114703d5bcbb73390688d62eed28671449369", "sha256_in_prefix": "85b98af0e0fa67b7d8ea1c229c7114703d5bcbb73390688d62eed28671449369", "size_in_bytes": 3266}, {"_path": "lib/python3.11/site-packages/wheel/vendored/packaging/_manylinux.py", "path_type": "hardlink", "sha256": "3fbb1d479ffb5c1634f4b55860f8479b274c2482303d75ac878a2593be14ba3e", "sha256_in_prefix": "3fbb1d479ffb5c1634f4b55860f8479b274c2482303d75ac878a2593be14ba3e", "size_in_bytes": 9588}, {"_path": "lib/python3.11/site-packages/wheel/vendored/packaging/_musllinux.py", "path_type": "hardlink", "sha256": "cf5b3c4e8da1434be99ff77e3b68b9ab11b010af1698694bb7777fdba57b35e6", "sha256_in_prefix": "cf5b3c4e8da1434be99ff77e3b68b9ab11b010af1698694bb7777fdba57b35e6", "size_in_bytes": 2674}, {"_path": "lib/python3.11/site-packages/wheel/vendored/packaging/_parser.py", "path_type": "hardlink", "sha256": "e2d4f87a64a5daa4da53b553404d576bda358cc3c2b017b3b18071c8d31437eb", "sha256_in_prefix": "e2d4f87a64a5daa4da53b553404d576bda358cc3c2b017b3b18071c8d31437eb", "size_in_bytes": 10347}, {"_path": "lib/python3.11/site-packages/wheel/vendored/packaging/_structures.py", "path_type": "hardlink", "sha256": "ab77953666d62461bf4b40e2b7f4b7028f2a42acffe4f6135c500a0597b9cabe", "sha256_in_prefix": "ab77953666d62461bf4b40e2b7f4b7028f2a42acffe4f6135c500a0597b9cabe", "size_in_bytes": 1431}, {"_path": "lib/python3.11/site-packages/wheel/vendored/packaging/_tokenizer.py", "path_type": "hardlink", "sha256": "6a50ad6f05e138502614667a050fb0093485a11009db3fb2b087fbfff31327f9", "sha256_in_prefix": "6a50ad6f05e138502614667a050fb0093485a11009db3fb2b087fbfff31327f9", "size_in_bytes": 5292}, {"_path": "lib/python3.11/site-packages/wheel/vendored/packaging/markers.py", "path_type": "hardlink", "sha256": "fd348f2350612583bb069f40cd398743122a1c45576938e60e1f46fb0f2accf0", "sha256_in_prefix": "fd348f2350612583bb069f40cd398743122a1c45576938e60e1f46fb0f2accf0", "size_in_bytes": 8232}, {"_path": "lib/python3.11/site-packages/wheel/vendored/packaging/requirements.py", "path_type": "hardlink", "sha256": "760a01795a6b3eed9813a43c9c67f038f4e30131db45afd918bc978451259fa4", "sha256_in_prefix": "760a01795a6b3eed9813a43c9c67f038f4e30131db45afd918bc978451259fa4", "size_in_bytes": 2933}, {"_path": "lib/python3.11/site-packages/wheel/vendored/packaging/specifiers.py", "path_type": "hardlink", "sha256": "2164add12acb48fef685e5a1002f142f4786bdab3b5c84078ea8958957e63ca1", "sha256_in_prefix": "2164add12acb48fef685e5a1002f142f4786bdab3b5c84078ea8958957e63ca1", "size_in_bytes": 39778}, {"_path": "lib/python3.11/site-packages/wheel/vendored/packaging/tags.py", "path_type": "hardlink", "sha256": "7de7475e2387901c4d6535e8b57bfcb973e630553d69ef93281ba38181e281c0", "sha256_in_prefix": "7de7475e2387901c4d6535e8b57bfcb973e630553d69ef93281ba38181e281c0", "size_in_bytes": 18950}, {"_path": "lib/python3.11/site-packages/wheel/vendored/packaging/utils.py", "path_type": "hardlink", "sha256": "5e07663f7cb1f7ec101058ceecebcc8fd46311fe49951e4714547af6fed243d1", "sha256_in_prefix": "5e07663f7cb1f7ec101058ceecebcc8fd46311fe49951e4714547af6fed243d1", "size_in_bytes": 5268}, {"_path": "lib/python3.11/site-packages/wheel/vendored/packaging/version.py", "path_type": "hardlink", "sha256": "3c525a6190f1060cb191f6211f7490c38a9f13d202096ad39a2b6fab5e32ddbb", "sha256_in_prefix": "3c525a6190f1060cb191f6211f7490c38a9f13d202096ad39a2b6fab5e32ddbb", "size_in_bytes": 16234}, {"_path": "lib/python3.11/site-packages/wheel/vendored/vendor.txt", "path_type": "hardlink", "sha256": "67610d8c1d62e69adf7b3f0274cd5276bddce99c6fdab451a253292e60677001", "sha256_in_prefix": "67610d8c1d62e69adf7b3f0274cd5276bddce99c6fdab451a253292e60677001", "size_in_bytes": 16}, {"_path": "lib/python3.11/site-packages/wheel/wheelfile.py", "path_type": "hardlink", "sha256": "5120adb4d949c1a7f1b79d5860514a1bb8e7c73f1d7e16f2a8064bea331303db", "sha256_in_prefix": "5120adb4d949c1a7f1b79d5860514a1bb8e7c73f1d7e16f2a8064bea331303db", "size_in_bytes": 8411}], "paths_version": 1}, "requested_spec": "None", "sha256": "42460d3e1974b1008ca94af2076a9220b01bd90e9a26f430316f0d90eedcb457", "size": 156109, "subdir": "osx-arm64", "timestamp": 1737990528000, "url": "https://repo.anaconda.com/pkgs/main/osx-arm64/wheel-0.45.1-py311hca03da5_0.conda", "version": "0.45.1"}