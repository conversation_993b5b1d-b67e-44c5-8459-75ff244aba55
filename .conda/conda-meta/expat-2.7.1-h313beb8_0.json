{"build": "h313beb8_0", "build_number": 0, "channel": "https://repo.anaconda.com/pkgs/main/osx-arm64", "constrains": [], "depends": ["libcxx >=14.0.6"], "extracted_package_dir": "/opt/homebrew/anaconda3/pkgs/expat-2.7.1-h313beb8_0", "files": ["bin/xmlwf", "include/expat.h", "include/expat_config.h", "include/expat_external.h", "lib/cmake/expat-2.7.1/expat-config-version.cmake", "lib/cmake/expat-2.7.1/expat-config.cmake", "lib/cmake/expat-2.7.1/expat-noconfig.cmake", "lib/cmake/expat-2.7.1/expat.cmake", "lib/libexpat.1.10.2.dylib", "lib/libexpat.1.dylib", "lib/libexpat.a", "lib/libexpat.dylib", "lib/pkgconfig/expat.pc", "share/doc/expat/AUTHORS", "share/doc/expat/changelog", "share/man/man1/xmlwf.1"], "fn": "expat-2.7.1-h313beb8_0.conda", "license": "MIT", "link": {"source": "/opt/homebrew/anaconda3/pkgs/expat-2.7.1-h313beb8_0", "type": 1}, "md5": "956c3776534a08f4037170d90c6e8b67", "name": "expat", "package_tarball_full_path": "/opt/homebrew/anaconda3/pkgs/expat-2.7.1-h313beb8_0.conda", "paths_data": {"paths": [{"_path": "bin/xmlwf", "path_type": "hardlink", "sha256": "c591cd474dc73de13c9b5f47b74044eb469e2ad7f0209a7831f4ab52e70d8f80", "sha256_in_prefix": "c591cd474dc73de13c9b5f47b74044eb469e2ad7f0209a7831f4ab52e70d8f80", "size_in_bytes": 73872}, {"_path": "include/expat.h", "path_type": "hardlink", "sha256": "7c16a5cf0eea844ae579db083b8d75f23a71859cac77e3c4cb7a8fa3b7621685", "sha256_in_prefix": "7c16a5cf0eea844ae579db083b8d75f23a71859cac77e3c4cb7a8fa3b7621685", "size_in_bytes": 44120}, {"_path": "include/expat_config.h", "path_type": "hardlink", "sha256": "48d5117b7e8376d435be3e9792de46e7cf4f3448b109e53bc17491b386bd7db0", "sha256_in_prefix": "48d5117b7e8376d435be3e9792de46e7cf4f3448b109e53bc17491b386bd7db0", "size_in_bytes": 4171}, {"_path": "include/expat_external.h", "path_type": "hardlink", "sha256": "7ca9ed28dd5e08eac425931894fabd4c876db8f4be26f9b9a33c2e4f70a0d6c3", "sha256_in_prefix": "7ca9ed28dd5e08eac425931894fabd4c876db8f4be26f9b9a33c2e4f70a0d6c3", "size_in_bytes": 6029}, {"_path": "lib/cmake/expat-2.7.1/expat-config-version.cmake", "path_type": "hardlink", "sha256": "5f42039a32496ebd9143f675bd8b3acc2136c2fc1efc585e9b12d338c2485342", "sha256_in_prefix": "5f42039a32496ebd9143f675bd8b3acc2136c2fc1efc585e9b12d338c2485342", "size_in_bytes": 2762}, {"_path": "lib/cmake/expat-2.7.1/expat-config.cmake", "path_type": "hardlink", "sha256": "33708544410c5b5fc7d1a25d1700804719deb4e4cb57358d4d9d2e7328418bcb", "sha256_in_prefix": "33708544410c5b5fc7d1a25d1700804719deb4e4cb57358d4d9d2e7328418bcb", "size_in_bytes": 3637}, {"_path": "lib/cmake/expat-2.7.1/expat-noconfig.cmake", "path_type": "hardlink", "sha256": "dd781a62a9557d5016d7b55bc267ab432bf941f7473a0a2f84b1346afad4cd0c", "sha256_in_prefix": "dd781a62a9557d5016d7b55bc267ab432bf941f7473a0a2f84b1346afad4cd0c", "size_in_bytes": 850}, {"_path": "lib/cmake/expat-2.7.1/expat.cmake", "path_type": "hardlink", "sha256": "861fd3e8d6ab95b5a4cfad1779367553b1275339d475b9089bc1e1e4a03a737d", "sha256_in_prefix": "861fd3e8d6ab95b5a4cfad1779367553b1275339d475b9089bc1e1e4a03a737d", "size_in_bytes": 4135}, {"_path": "lib/libexpat.1.10.2.dylib", "path_type": "hardlink", "sha256": "a4f8d36878806bd17cbb1643c3cb619ffabe32859e6bb094aa018351e8f85158", "sha256_in_prefix": "a4f8d36878806bd17cbb1643c3cb619ffabe32859e6bb094aa018351e8f85158", "size_in_bytes": 199152}, {"_path": "lib/libexpat.1.dylib", "path_type": "softlink", "sha256": "a4f8d36878806bd17cbb1643c3cb619ffabe32859e6bb094aa018351e8f85158", "size_in_bytes": 199152}, {"_path": "lib/libexpat.a", "path_type": "hardlink", "sha256": "b3b1f976c22f7918c9b0970842a148f7bfc82a338cd1b436d83dd5012debf689", "sha256_in_prefix": "b3b1f976c22f7918c9b0970842a148f7bfc82a338cd1b436d83dd5012debf689", "size_in_bytes": 194544}, {"_path": "lib/libexpat.dylib", "path_type": "softlink", "sha256": "a4f8d36878806bd17cbb1643c3cb619ffabe32859e6bb094aa018351e8f85158", "size_in_bytes": 199152}, {"_path": "lib/pkgconfig/expat.pc", "file_mode": "text", "path_type": "hardlink", "prefix_placeholder": "/var/folders/nz/j6p8yfhx1mv_0grj5xl4650h0000gp/T/abs_2alydm3lm2/croot/expat_1744659823361/_h_env_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehol", "sha256": "fac62f131b510d86f022d7b04d197885b6a085562acf767ec235f91ef01e1c6f", "sha256_in_prefix": "24aefdb11a8f39ee50368a404a6331eb01960330d282178cf1559c7393ffb1c8", "size_in_bytes": 525}, {"_path": "share/doc/expat/AUTHORS", "path_type": "hardlink", "sha256": "59f14371c6b75912cfebb46e6247ee5146766e803a0365b124e5d3011e7d0877", "sha256_in_prefix": "59f14371c6b75912cfebb46e6247ee5146766e803a0365b124e5d3011e7d0877", "size_in_bytes": 142}, {"_path": "share/doc/expat/changelog", "path_type": "hardlink", "sha256": "9d8bb6625d503b969882fa15178b450ac0ccffdfdac1c720f74d92de243e3580", "sha256_in_prefix": "9d8bb6625d503b969882fa15178b450ac0ccffdfdac1c720f74d92de243e3580", "size_in_bytes": 84961}, {"_path": "share/man/man1/xmlwf.1", "path_type": "hardlink", "sha256": "3d7a6e32305cb05a677cf7576ce6715f23cb613d542671509ea1f7eca81f7f25", "sha256_in_prefix": "3d7a6e32305cb05a677cf7576ce6715f23cb613d542671509ea1f7eca81f7f25", "size_in_bytes": 10944}], "paths_version": 1}, "requested_spec": "None", "sha256": "a6c4e675e8903418fa395ac3998aceb0d98e60031add4de1280b2663f6506d25", "size": 160099, "subdir": "osx-arm64", "timestamp": 1744659879000, "url": "https://repo.anaconda.com/pkgs/main/osx-arm64/expat-2.7.1-h313beb8_0.conda", "version": "2.7.1"}