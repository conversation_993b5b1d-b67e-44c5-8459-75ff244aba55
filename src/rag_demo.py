"""
RAG系统演示和测试脚本
展示如何使用RAG系统进行文档检索和问答
"""

from rag_system import RAGSystem, create_sample_documents
import os


def demo_basic_usage():
    """演示RAG系统的基本使用"""
    print("=== RAG系统基本使用演示 ===\n")
    
    # 创建RAG系统实例
    rag = RAGSystem(db_path="demo_rag.db")
    
    # 添加示例文档
    print("1. 添加示例文档...")
    sample_docs = create_sample_documents()
    rag.add_documents(sample_docs)
    
    # 显示系统统计信息
    stats = rag.get_statistics()
    print(f"\n系统统计信息:")
    print(f"  - 文档总数: {stats['total_documents']}")
    print(f"  - 词汇表大小: {stats['vocabulary_size']}")
    print(f"  - 训练状态: {'已训练' if stats['is_trained'] else '未训练'}")
    
    # 测试查询
    print(f"\n2. 测试查询功能...")
    test_questions = [
        "什么是人工智能？",
        "机器学习是什么？",
        "深度学习和机器学习的关系？",
        "自然语言处理的应用有哪些？",
        "计算机视觉能做什么？"
    ]
    
    for i, question in enumerate(test_questions, 1):
        print(f"\n--- 查询 {i} ---")
        print(f"问题: {question}")
        
        result = rag.query(question, top_k=3)
        print(f"答案: {result['answer']}")
        
        print("检索到的相关文档:")
        for j, doc in enumerate(result['retrieved_docs'], 1):
            print(f"  {j}. 文档ID: {doc['doc_id']}")
            print(f"     相似度: {doc['similarity']:.4f}")
            print(f"     内容预览: {doc['content']}")
            print(f"     元数据: {doc['metadata']}")
            print()


def demo_custom_documents():
    """演示添加自定义文档"""
    print("\n=== 自定义文档演示 ===\n")
    
    # 创建新的RAG系统实例
    rag = RAGSystem(db_path="custom_rag.db")
    
    # 添加自定义文档
    custom_docs = [
        {
            'id': 'python_intro',
            'content': 'Python是一种高级编程语言，由Guido van Rossum于1991年首次发布。Python设计哲学强调代码的可读性和简洁的语法。Python支持多种编程范式，包括面向对象、命令式、函数式和过程式编程。',
            'metadata': {'language': 'Python', 'type': '介绍', 'year': 1991}
        },
        {
            'id': 'python_features',
            'content': 'Python的主要特性包括：动态类型系统、自动内存管理、丰富的标准库、跨平台兼容性、简洁的语法。Python广泛应用于Web开发、数据科学、人工智能、自动化脚本等领域。',
            'metadata': {'language': 'Python', 'type': '特性'}
        },
        {
            'id': 'python_libraries',
            'content': 'Python拥有丰富的第三方库生态系统。在数据科学领域，常用的库包括NumPy、Pandas、Matplotlib、Scikit-learn。在Web开发中，Django和Flask是最受欢迎的框架。',
            'metadata': {'language': 'Python', 'type': '库和框架'}
        }
    ]
    
    print("添加Python相关文档...")
    rag.add_documents(custom_docs)
    
    # 测试Python相关查询
    python_questions = [
        "Python是什么时候发布的？",
        "Python有哪些特性？",
        "Python在数据科学中用什么库？"
    ]
    
    for question in python_questions:
        print(f"\n问题: {question}")
        result = rag.query(question)
        print(f"答案: {result['answer']}")


def demo_interactive_mode():
    """交互式演示模式"""
    print("\n=== 交互式问答演示 ===")
    print("输入 'quit' 或 'exit' 退出")
    
    # 使用之前创建的RAG系统
    rag = RAGSystem(db_path="demo_rag.db")
    
    # 检查是否已有数据，如果没有则添加示例文档
    stats = rag.get_statistics()
    if stats['total_documents'] == 0:
        print("检测到空数据库，正在添加示例文档...")
        sample_docs = create_sample_documents()
        rag.add_documents(sample_docs)
    
    while True:
        try:
            question = input("\n请输入您的问题: ").strip()
            
            if question.lower() in ['quit', 'exit', '退出']:
                print("感谢使用RAG系统！")
                break
            
            if not question:
                print("请输入有效的问题。")
                continue
            
            print("正在搜索相关文档...")
            result = rag.query(question)
            
            print(f"\n答案: {result['answer']}")
            
            # 询问是否显示详细的检索结果
            show_details = input("\n是否显示详细的检索结果？(y/n): ").strip().lower()
            if show_details in ['y', 'yes', '是']:
                print("\n检索到的相关文档:")
                for i, doc in enumerate(result['retrieved_docs'], 1):
                    print(f"\n{i}. 文档ID: {doc['doc_id']}")
                    print(f"   相似度: {doc['similarity']:.4f}")
                    print(f"   内容: {doc['content']}")
                    if doc['metadata']:
                        print(f"   元数据: {doc['metadata']}")
        
        except KeyboardInterrupt:
            print("\n\n程序被用户中断，退出...")
            break
        except Exception as e:
            print(f"发生错误: {e}")


def cleanup_demo_files():
    """清理演示文件"""
    demo_files = ["demo_rag.db", "custom_rag.db", "rag_vectors.db"]
    
    print("\n=== 清理演示文件 ===")
    for file in demo_files:
        if os.path.exists(file):
            os.remove(file)
            print(f"已删除: {file}")
        else:
            print(f"文件不存在: {file}")


def main():
    """主函数 - 运行所有演示"""
    print("RAG系统完整演示")
    print("=" * 50)
    
    try:
        # 基本使用演示
        demo_basic_usage()
        
        # 自定义文档演示
        demo_custom_documents()
        
        # 交互式模式
        interactive = input("\n是否进入交互式问答模式？(y/n): ").strip().lower()
        if interactive in ['y', 'yes', '是']:
            demo_interactive_mode()
        
    except Exception as e:
        print(f"演示过程中发生错误: {e}")
    
    finally:
        # 询问是否清理文件
        cleanup = input("\n是否清理演示生成的数据库文件？(y/n): ").strip().lower()
        if cleanup in ['y', 'yes', '是']:
            cleanup_demo_files()


if __name__ == "__main__":
    main()
