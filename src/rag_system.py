"""
RAG (Retrieval-Augmented Generation) 系统实现
使用标准Python库实现的简单但功能完整的RAG系统
"""

import json
import math
import re
import sqlite3
from collections import Counter, defaultdict
from typing import List, Dict, Tuple, Optional
import hashlib
import os


class TextProcessor:
    """文本处理器 - 负责文档的预处理和分词"""
    
    def __init__(self):
        # 中文标点符号和英文标点符号
        self.punctuation = '！？｡。＂＃＄％＆＇（）＊＋，－／：；＜＝＞＠［＼］＾＿｀｛｜｝～｟｠｢｣､、〃》「」『』【】〔〕〖〗〘〙〚〛〜〝〞〟〰〱〲〳〴〵〶〷〸〹〺〻〼〽〾〿!"#$%&\'()*+,-./:;<=>?@[\\]^_`{|}~'
        
    def clean_text(self, text: str) -> str:
        """清理文本，去除多余的空白字符和标点"""
        # 去除多余的空白字符
        text = re.sub(r'\s+', ' ', text)
        # 去除首尾空白
        text = text.strip()
        return text
    
    def tokenize(self, text: str) -> List[str]:
        """简单的分词实现 - 支持中英文混合文本"""
        # 清理文本
        text = self.clean_text(text)
        
        # 使用正则表达式分词：中文字符、英文单词、数字
        pattern = r'[\u4e00-\u9fff]|[a-zA-Z]+|\d+'
        tokens = re.findall(pattern, text.lower())
        
        # 过滤掉长度小于2的英文单词（保留中文字符）
        filtered_tokens = []
        for token in tokens:
            if re.match(r'[a-zA-Z]+', token):
                if len(token) >= 2:  # 英文单词至少2个字符
                    filtered_tokens.append(token)
            else:
                filtered_tokens.append(token)  # 中文字符和数字直接保留
                
        return filtered_tokens
    
    def extract_ngrams(self, tokens: List[str], n: int = 2) -> List[str]:
        """提取n-gram特征"""
        if len(tokens) < n:
            return tokens
        
        ngrams = []
        for i in range(len(tokens) - n + 1):
            ngram = ' '.join(tokens[i:i+n])
            ngrams.append(ngram)
        return ngrams


class TFIDFVectorizer:
    """TF-IDF向量化器 - 将文本转换为向量表示"""
    
    def __init__(self, max_features: int = 10000):
        self.max_features = max_features
        self.vocabulary = {}  # 词汇表：{词: 索引}
        self.idf_scores = {}  # IDF分数：{词: IDF值}
        self.fitted = False
        
    def fit(self, documents: List[str]) -> None:
        """训练TF-IDF模型"""
        processor = TextProcessor()
        
        # 统计词频和文档频率
        doc_freq = Counter()  # 文档频率
        all_tokens = set()
        
        tokenized_docs = []
        for doc in documents:
            tokens = processor.tokenize(doc)
            tokenized_docs.append(tokens)
            
            # 统计每个文档中出现的唯一词汇
            unique_tokens = set(tokens)
            for token in unique_tokens:
                doc_freq[token] += 1
            all_tokens.update(tokens)
        
        # 选择最频繁的词汇作为特征
        most_common_tokens = doc_freq.most_common(self.max_features)
        
        # 构建词汇表
        self.vocabulary = {token: idx for idx, (token, _) in enumerate(most_common_tokens)}
        
        # 计算IDF分数
        total_docs = len(documents)
        for token, freq in most_common_tokens:
            # IDF = log(总文档数 / 包含该词的文档数)
            self.idf_scores[token] = math.log(total_docs / freq)
        
        self.fitted = True
    
    def transform(self, documents: List[str]) -> List[List[float]]:
        """将文档转换为TF-IDF向量"""
        if not self.fitted:
            raise ValueError("必须先调用fit方法训练模型")
        
        processor = TextProcessor()
        vectors = []
        
        for doc in documents:
            tokens = processor.tokenize(doc)
            
            # 计算词频
            token_freq = Counter(tokens)
            total_tokens = len(tokens)
            
            # 构建TF-IDF向量
            vector = [0.0] * len(self.vocabulary)
            
            for token, freq in token_freq.items():
                if token in self.vocabulary:
                    idx = self.vocabulary[token]
                    # TF = 词频 / 总词数
                    tf = freq / total_tokens
                    # TF-IDF = TF * IDF
                    tfidf = tf * self.idf_scores[token]
                    vector[idx] = tfidf
            
            vectors.append(vector)
        
        return vectors
    
    def fit_transform(self, documents: List[str]) -> List[List[float]]:
        """训练并转换文档"""
        self.fit(documents)
        return self.transform(documents)


class VectorDatabase:
    """向量数据库 - 存储和检索文档向量"""
    
    def __init__(self, db_path: str = "rag_vectors.db"):
        self.db_path = db_path
        self.init_database()
    
    def init_database(self):
        """初始化数据库"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # 创建文档表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS documents (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                doc_id TEXT UNIQUE,
                content TEXT,
                metadata TEXT,
                vector_data TEXT
            )
        ''')
        
        conn.commit()
        conn.close()
    
    def add_document(self, doc_id: str, content: str, vector: List[float], 
                    metadata: Optional[Dict] = None):
        """添加文档到数据库"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # 将向量和元数据序列化为JSON
        vector_json = json.dumps(vector)
        metadata_json = json.dumps(metadata or {})
        
        cursor.execute('''
            INSERT OR REPLACE INTO documents (doc_id, content, metadata, vector_data)
            VALUES (?, ?, ?, ?)
        ''', (doc_id, content, metadata_json, vector_json))
        
        conn.commit()
        conn.close()
    
    def get_all_documents(self) -> List[Tuple[str, str, List[float], Dict]]:
        """获取所有文档"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('SELECT doc_id, content, vector_data, metadata FROM documents')
        results = cursor.fetchall()
        
        documents = []
        for doc_id, content, vector_json, metadata_json in results:
            vector = json.loads(vector_json)
            metadata = json.loads(metadata_json)
            documents.append((doc_id, content, vector, metadata))
        
        conn.close()
        return documents
    
    def cosine_similarity(self, vec1: List[float], vec2: List[float]) -> float:
        """计算余弦相似度"""
        # 计算点积
        dot_product = sum(a * b for a, b in zip(vec1, vec2))
        
        # 计算向量的模长
        norm1 = math.sqrt(sum(a * a for a in vec1))
        norm2 = math.sqrt(sum(a * a for a in vec2))
        
        # 避免除零错误
        if norm1 == 0 or norm2 == 0:
            return 0.0
        
        return dot_product / (norm1 * norm2)
    
    def search(self, query_vector: List[float], top_k: int = 5) -> List[Tuple[str, str, float, Dict]]:
        """搜索最相似的文档"""
        documents = self.get_all_documents()
        
        # 计算查询向量与所有文档向量的相似度
        similarities = []
        for doc_id, content, doc_vector, metadata in documents:
            similarity = self.cosine_similarity(query_vector, doc_vector)
            similarities.append((doc_id, content, similarity, metadata))
        
        # 按相似度降序排序，返回top_k个结果
        similarities.sort(key=lambda x: x[2], reverse=True)
        return similarities[:top_k]


class SimpleGenerator:
    """简单的文本生成器 - 基于检索到的文档生成回答"""
    
    def __init__(self):
        pass
    
    def generate_answer(self, query: str, retrieved_docs: List[Tuple[str, str, float, Dict]], 
                       max_length: int = 500) -> str:
        """基于检索到的文档生成回答"""
        if not retrieved_docs:
            return "抱歉，没有找到相关的文档来回答您的问题。"
        
        # 提取最相关的文档内容
        relevant_contents = []
        for doc_id, content, similarity, metadata in retrieved_docs:
            if similarity > 0.1:  # 只使用相似度大于0.1的文档
                relevant_contents.append(content)
        
        if not relevant_contents:
            return "抱歉，没有找到足够相关的文档来回答您的问题。"
        
        # 简单的答案生成策略：
        # 1. 找到包含查询关键词的句子
        # 2. 组合这些句子形成答案
        
        processor = TextProcessor()
        query_tokens = set(processor.tokenize(query))
        
        relevant_sentences = []
        for content in relevant_contents:
            # 简单的句子分割
            sentences = re.split(r'[。！？.!?]', content)
            
            for sentence in sentences:
                sentence = sentence.strip()
                if len(sentence) < 10:  # 过滤太短的句子
                    continue
                
                sentence_tokens = set(processor.tokenize(sentence))
                # 计算句子与查询的词汇重叠度
                overlap = len(query_tokens & sentence_tokens)
                if overlap > 0:
                    relevant_sentences.append((sentence, overlap))
        
        if not relevant_sentences:
            # 如果没有找到重叠的句子，返回最相关文档的开头部分
            return f"根据相关文档，{relevant_contents[0][:max_length]}..."
        
        # 按重叠度排序，选择最相关的句子
        relevant_sentences.sort(key=lambda x: x[1], reverse=True)
        
        # 组合答案
        answer_parts = []
        current_length = 0
        
        for sentence, _ in relevant_sentences:
            if current_length + len(sentence) > max_length:
                break
            answer_parts.append(sentence)
            current_length += len(sentence)
        
        if answer_parts:
            return "根据相关文档：" + "。".join(answer_parts) + "。"
        else:
            return f"根据相关文档，{relevant_contents[0][:max_length]}..."


class RAGSystem:
    """RAG系统主类 - 整合检索和生成功能"""

    def __init__(self, db_path: str = "rag_vectors.db", max_features: int = 10000):
        """
        初始化RAG系统

        Args:
            db_path: 向量数据库路径
            max_features: TF-IDF最大特征数
        """
        self.vectorizer = TFIDFVectorizer(max_features=max_features)
        self.vector_db = VectorDatabase(db_path)
        self.generator = SimpleGenerator()
        self.is_trained = False

    def add_documents(self, documents: List[Dict[str, str]]) -> None:
        """
        添加文档到RAG系统

        Args:
            documents: 文档列表，每个文档包含 {'id': str, 'content': str, 'metadata': dict}
        """
        # 提取文档内容用于训练向量化器
        contents = [doc['content'] for doc in documents]

        # 训练或更新TF-IDF向量化器
        if not self.is_trained:
            print("正在训练TF-IDF向量化器...")
            self.vectorizer.fit(contents)
            self.is_trained = True

        # 将文档转换为向量并存储
        print("正在向量化文档...")
        vectors = self.vectorizer.transform(contents)

        print("正在存储文档到向量数据库...")
        for doc, vector in zip(documents, vectors):
            doc_id = doc['id']
            content = doc['content']
            metadata = doc.get('metadata', {})

            self.vector_db.add_document(doc_id, content, vector, metadata)

        print(f"成功添加 {len(documents)} 个文档到RAG系统")

    def query(self, question: str, top_k: int = 5) -> Dict[str, any]:
        """
        查询RAG系统

        Args:
            question: 用户问题
            top_k: 返回最相关的文档数量

        Returns:
            包含答案和检索到的文档的字典
        """
        if not self.is_trained:
            return {
                'answer': '系统尚未训练，请先添加文档。',
                'retrieved_docs': [],
                'query': question
            }

        # 将查询转换为向量
        query_vector = self.vectorizer.transform([question])[0]

        # 检索相关文档
        retrieved_docs = self.vector_db.search(query_vector, top_k)

        # 生成答案
        answer = self.generator.generate_answer(question, retrieved_docs)

        return {
            'answer': answer,
            'retrieved_docs': [
                {
                    'doc_id': doc_id,
                    'content': content[:200] + '...' if len(content) > 200 else content,
                    'similarity': similarity,
                    'metadata': metadata
                }
                for doc_id, content, similarity, metadata in retrieved_docs
            ],
            'query': question
        }

    def get_statistics(self) -> Dict[str, any]:
        """获取系统统计信息"""
        documents = self.vector_db.get_all_documents()

        return {
            'total_documents': len(documents),
            'vocabulary_size': len(self.vectorizer.vocabulary) if self.is_trained else 0,
            'is_trained': self.is_trained,
            'database_path': self.vector_db.db_path
        }


def create_sample_documents() -> List[Dict[str, str]]:
    """创建示例文档用于测试"""
    return [
        {
            'id': 'doc1',
            'content': '人工智能是计算机科学的一个分支，它试图理解智能的实质，并生产出一种新的能以人类智能相似的方式做出反应的智能机器。人工智能研究的一个主要目标是使机器能够胜任一些通常需要人类智能才能完成的复杂工作。',
            'metadata': {'source': 'AI教科书', 'chapter': '第一章'}
        },
        {
            'id': 'doc2',
            'content': '机器学习是人工智能的一个重要分支，它是一种通过算法解析数据、从中学习，然后对真实世界中的事件做出决策和预测的方法。机器学习算法不是依赖于预先编程的指令，而是依赖于数据来构建数学模型。',
            'metadata': {'source': 'ML指南', 'topic': '机器学习基础'}
        },
        {
            'id': 'doc3',
            'content': '深度学习是机器学习的一个子集，它模仿人脑的工作方式来处理数据并创建用于决策的模式。深度学习使用人工神经网络，这些网络具有多个层次，可以学习数据的复杂表示。',
            'metadata': {'source': '深度学习教程', 'level': '中级'}
        },
        {
            'id': 'doc4',
            'content': '自然语言处理（NLP）是人工智能和语言学的交叉领域，专注于让计算机理解、解释和生成人类语言。NLP的应用包括机器翻译、情感分析、文本摘要和问答系统。',
            'metadata': {'source': 'NLP手册', 'applications': ['翻译', '问答']}
        },
        {
            'id': 'doc5',
            'content': '计算机视觉是人工智能的一个领域，致力于让机器能够识别和理解数字图像和视频中的内容。它包括图像识别、物体检测、人脸识别和图像生成等技术。',
            'metadata': {'source': 'CV教程', 'techniques': ['识别', '检测']}
        }
    ]


if __name__ == "__main__":
    # 示例使用
    print("=== RAG系统演示 ===")

    # 创建RAG系统实例
    rag = RAGSystem()

    # 添加示例文档
    sample_docs = create_sample_documents()
    rag.add_documents(sample_docs)

    # 查询示例
    questions = [
        "什么是人工智能？",
        "机器学习和深度学习有什么区别？",
        "自然语言处理有哪些应用？"
    ]

    for question in questions:
        print(f"\n问题: {question}")
        result = rag.query(question)
        print(f"答案: {result['answer']}")
        print("相关文档:")
        for doc in result['retrieved_docs'][:3]:  # 只显示前3个
            print(f"  - {doc['doc_id']}: 相似度 {doc['similarity']:.3f}")

    # 显示系统统计信息
    stats = rag.get_statistics()
    print(f"\n=== 系统统计 ===")
    print(f"文档总数: {stats['total_documents']}")
    print(f"词汇表大小: {stats['vocabulary_size']}")
    print(f"是否已训练: {stats['is_trained']}")
